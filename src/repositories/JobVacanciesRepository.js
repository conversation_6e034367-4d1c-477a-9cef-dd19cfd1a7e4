const { Op } = require('sequelize');
const BaseRepository = require('./BaseRepository');
const { JobVacancy, JobTitle } = require('../models');

/**
 * JobVacanciesRepository - Repository for job vacancy read operations
 * Extends BaseRepository with job vacancy-specific filtering and scoping
 */
class JobVacanciesRepository extends BaseRepository {
  constructor() {
    super(JobVacancy);
  }

  /**
   * Override findAll to include JobTitle association by default
   * @param {Object} queryParams - Query parameters from request
   * @returns {Object} Records with pagination info
   */
  async findAll(queryParams = {}) {
    const {
      page = 1,
      limit = 10,
      sort,
      sort_direction,
      without_count,
      ...filterParams
    } = queryParams;

    let count = null;
    let rows = null;

    let validPage = parseInt(page);
    if (validPage < 1) validPage = 1;

    let validLimit = parseInt(limit);
    if (validLimit < 1) validLimit = 1;
    if (validLimit > 100) validLimit = 100;

    // Build query options with JobTitle association
    const queryOptions = {
      where: this.buildWhereClause(filterParams),
      limit: validLimit,
      offset: (validPage - 1) * validLimit,
      order: this.buildOrderClause(sort, sort_direction),
      include: [
        {
          model: JobTitle,
          as: 'jobTitle',
          attributes: ['id', 'name'],
        },
      ],
    };

    // Execute query
    if (without_count) {
      rows = await this.model.findAll(queryOptions);
    } else {
      const result = await this.model.findAndCountAll(queryOptions);
      count = result.count;
      rows = result.rows;
    }

    return {
      rows,
      pagination: {
        page: validPage,
        limit: validLimit,
        total: count,
      },
    };
  }

  /**
   * Find a single job vacancy by ID with JobTitle association
   * @param {number} id - Job vacancy ID
   * @returns {Object|null} Found job vacancy or null
   */
  async findById(id) {
    return await this.model.findByPk(id, {
      include: [
        {
          model: JobTitle,
          as: 'jobTitle',
          attributes: ['id', 'name'],
        },
      ],
    });
  }

  /**
   * Filter job vacancies by department
   * Usage: /api/v1/job_vacancies?department=engineering
   * @param {string} department - Department to filter by
   * @returns {Object} Where condition for department filter
   */
  filterByDepartment(department) {
    if (!department) return null;

    return {
      department: {
        [Op.iLike]: `%${department}%`,
      },
    };
  }

  /**
   * Filter job vacancies by job grade
   * Usage: /api/v1/job_vacancies?job_grade=senior
   * @param {string} jobGrade - Job grade to filter by
   * @returns {Object} Where condition for job grade filter
   */
  filterByJobGrade(jobGrade) {
    if (!jobGrade) return null;

    return {
      job_grade: {
        [Op.iLike]: `%${jobGrade}%`,
      },
    };
  }

  /**
   * Filter job vacancies by job title ID
   * Usage: /api/v1/job_vacancies?job_title_id=1
   * @param {number} jobTitleId - Job title ID to filter by
   * @returns {Object} Where condition for job title ID filter
   */
  filterByJobTitleId(jobTitleId) {
    if (!jobTitleId) return null;

    const id = parseInt(jobTitleId);
    if (isNaN(id) || id < 1) return null;

    return {
      job_title_id: id,
    };
  }

  /**
   * Filter job vacancies by search term (searches in name and job description)
   * Usage: /api/v1/job_vacancies?search=developer
   * @param {string} search - Search term
   * @returns {Object} Where condition for search filter
   */
  filterBySearch(search) {
    if (!search) return null;

    return {
      [Op.or]: [
        {
          name: {
            [Op.iLike]: `%${search}%`,
          },
        },
        {
          job_description: {
            [Op.iLike]: `%${search}%`,
          },
        },
      ],
    };
  }
}

module.exports = JobVacanciesRepository;
