const ApiOutput = require('./ApiOutput');

/**
 * Output formatting for job vacancies
 */
class JobVacancyOutput extends ApiOutput {
  /**
   * Format a single job vacancy for output
   * @param {Object} item - Job vacancy item to format (optional, uses this.data if not provided)
   * @returns {Object} Formatted job vacancy data
   */
  format() {
    const vacancy = this.data;

    return {
      id: vacancy.id,
      name: vacancy.name,
      department: vacancy.department,
      job_grade: vacancy.job_grade,
      job_description: vacancy.job_description,
      competencies: vacancy.competencies || [],
      skills: vacancy.skills || [],
      job_title_id: vacancy.job_title_id,
      job_title: vacancy.jobTitle
        ? {
            id: vacancy.jobTitle.id,
            name: vacancy.jobTitle.name,
          }
        : null,
      created_at: vacancy.created_at,
      updated_at: vacancy.updated_at,
    };
  }
}

module.exports = JobVacancyOutput;
