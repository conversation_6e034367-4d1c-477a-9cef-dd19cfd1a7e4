const ApiOutput = require('./ApiOutput');

/**
 * Output formatting for job vacancies
 */
class JobVacancyOutput extends ApiOutput {
  /**
   * Format a single job vacancy for output
   * @param {Object} item - Job vacancy item to format (optional, uses this.data if not provided)
   * @returns {Object} Formatted job vacancy data
   */
  format() {
    return {
      id: this.data.id,
      name: this.data.name,
    };
  }
}

module.exports = JobVacancyOutput;
