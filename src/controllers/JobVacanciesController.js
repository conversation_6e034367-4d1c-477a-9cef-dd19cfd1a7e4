const ApiController = require('./ApiController');
const JobVacancyService = require('../services/JobVacancyService');
const JobVacanciesCreateInput = require('../inputs/JobVacanciesCreateInput');
const JobVacanciesIndexInput = require('../inputs/JobVacanciesIndexInput');
const JobVacancyOutput = require('../outputs/JobVacancyOutput');

/**
 * Controller for job vacancy operations
 */
class JobVacanciesController extends ApiController {
  constructor() {
    super();
    this.service = new JobVacancyService();
  }

  /**
   * Get all job vacancies (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    const input = new JobVacanciesIndexInput(req.query);
    input.validate();

    const result = await this.service.findAll(input.output());
    const output = new JobVacancyOutput(result.job_vacancies, {
      pagination: result.pagination,
    });

    output.renderJsonArray(res);
  });

  /**
   * Get a specific job vacancy by ID (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  show = this.createMethod(async (req, res) => {
    const { id } = req.params;
    const parsedId = parseInt(id, 10);

    const vacancy = await this.service.findById(parsedId);
    const output = new JobVacancyOutput(vacancy);
    output.renderJson(res);
  });

  /**
   * Create a new job vacancy (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  create = this.createMethod(async (req, res) => {
    const input = new JobVacanciesCreateInput(req.body);
    input.validate();

    const vacancy = await this.service.create(input.output());
    const output = new JobVacancyOutput(vacancy, { statusCode: 201 });
    output.renderJson(res);
  });
}

module.exports = new JobVacanciesController();
