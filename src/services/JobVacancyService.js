const AppService = require('./AppService');
const { JobVacancy, JobTitle } = require('../models');
const JobVacanciesRepository = require('../repositories/JobVacanciesRepository');

/**
 * Service class for job vacancy operations
 */
class JobVacancyService extends AppService {
  constructor() {
    super();
    this.repository = new JobVacanciesRepository();
  }
  /**
   * Get all job vacancies
   * @param {Object} params - Query params (page, limit, filters, etc.)
   * @returns {Object} Job vacancies array and pagination info
   */
  async findAll(params = {}) {
    const { rows, pagination } = await this.repository.findAll(params);
    return { job_vacancies: rows, pagination };
  }

  /**
   * Find a job vacancy by ID
   * @param {number} id - Job vacancy ID
   * @returns {Object} Job vacancy object
   * @throws {NotFoundError} If job vacancy is not found
   */
  async findById(id) {
    const vacancy = await this.repository.findById(id);
    this.exists(vacancy, 'Job vacancy not found');
    return vacancy;
  }

  /**
   * Create a new job vacancy
   * @param {Object} data - Job vacancy data
   * @returns {Object} Created job vacancy
   * @throws {NotFoundError} If job title is not found
   */
  async create(data) {
    // 1. Validate that the JobTitle exists
    const jobTitle = await JobTitle.findByPk(data.job_title_id);
    this.exists(jobTitle, 'Job title not found');

    // 2. Prepare data for creation, using the JobTitle's name
    const vacancyData = {
      ...data,
      name: jobTitle.name, // Set the name from the associated JobTitle
    };

    // 3. Create the vacancy
    const vacancy = await JobVacancy.create(vacancyData);

    // 4. Return the vacancy with the job title included
    return this.findById(vacancy.id);
  }
}

module.exports = JobVacancyService;
