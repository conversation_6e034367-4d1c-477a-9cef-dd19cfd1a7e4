const ApplicationInput = require('./ApplicationInput');

/**
 * Input validation class for job vacancies index
 */
class JobVacanciesIndexInput extends ApplicationInput {
  /**
   * Define the JSON schema for job vacancies index validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        search: {
          type: 'string',
        },
        department: {
          type: 'string',
        },
        job_grade: {
          type: 'string',
        },
        job_title_id: {
          type: 'integer',
          minimum: 1,
        },
        sort: {
          type: 'string',
          enum: ['id', 'name', 'department', 'job_grade', 'created_at', 'updated_at'],
        },
        sort_direction: {
          type: 'string',
          enum: ['asc', 'desc'],
        },
        page: {
          type: 'integer',
          minimum: 1,
        },
        limit: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
        },
      },
      required: [],
      additionalProperties: false,
    };
  }
}

module.exports = JobVacanciesIndexInput;
